# -*- coding: utf-8 -*-
"""
测试套利信息提取器的新报告格式

作者: AI助手
日期: 2025-07-29
功能: 测试修改后的套利信息提取器是否能生成符合微信发布器格式的报告
"""

import os
import sys
import json
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))

from 套利信息提取器 import 套利信息提取器


def 创建测试数据():
    """创建测试用的套利信息数据"""
    return [
        {
            '文件名': '测试文章1.txt',
            '标题': '今日新股申购机会分析',
            '发布时间': '2025-07-29 10:00:00',
            '链接': 'https://example.com/article1',
            '新股信息': [
                {
                    '股票名称': '科技创新',
                    '相关信息': '科技创新今日申购，发行价格25.8元，预计收益率15%，建议积极参与申购'
                },
                {
                    '股票名称': '智能制造',
                    '相关信息': '智能制造明日申购，发行价格18.5元，市盈率较低，具有较好投资价值'
                }
            ],
            'REIT信息': [
                {
                    'REIT名称': '产业园REIT',
                    '相关信息': '产业园REIT本周上市，年化收益率预期4.5%，适合稳健投资者'
                }
            ],
            '转债信息': [
                {
                    '转债名称': '科技转债',
                    '相关信息': '科技转债当前价格98元，转股溢价率5%，具有套利空间'
                }
            ],
            '套利机会': [
                '新股申购中签率预计0.5%，预期收益15%，年化收益率可观',
                'REIT折价发行，上市后预期有5-10%涨幅空间'
            ],
            '操作建议': [
                '建议满额申购新股，提高中签概率',
                '关注REIT上市首日表现，择机介入'
            ],
            '时间节点': [
                '今日16:00前完成新股申购',
                '明日公布中签结果'
            ]
        },
        {
            '文件名': '测试文章2.txt',
            '标题': '转债套利机会分析',
            '发布时间': '2025-07-29 14:00:00',
            '链接': 'https://example.com/article2',
            '新股信息': [],
            'REIT信息': [],
            '转债信息': [
                {
                    '转债名称': '银行转债',
                    '相关信息': '银行转债价格低于面值，安全边际较高，适合保守投资者'
                },
                {
                    '转债名称': '医药转债',
                    '相关信息': '医药转债正股表现强势，转债跟涨空间较大'
                }
            ],
            '套利机会': [
                '银行转债下有保底，上不封顶，风险收益比优秀',
                '医药转债受益于行业复苏，中长期看好'
            ],
            '操作建议': [
                '分批建仓转债，控制仓位风险',
                '设置止盈止损点位，严格执行'
            ],
            '时间节点': [
                '本周五转债申购截止',
                '下周一公布配售结果'
            ]
        }
    ]


def 测试报告格式():
    """测试报告格式是否符合微信发布器要求"""
    print("🧪 开始测试套利信息提取器的新报告格式")
    print("=" * 60)
    
    # 创建提取器实例
    提取器 = 套利信息提取器()
    
    # 创建测试数据
    测试数据 = 创建测试数据()
    print(f"📊 创建了 {len(测试数据)} 条测试数据")
    
    # 生成报告
    print("\n🔄 生成套利报告...")
    报告数据 = 提取器.生成套利报告(测试数据)
    
    # 验证报告格式
    print("\n✅ 验证报告格式:")
    
    # 检查必需字段
    必需字段 = ['标题', '内容', '元数据']
    for 字段 in 必需字段:
        if 字段 in 报告数据:
            print(f"   ✓ {字段}: 存在")
        else:
            print(f"   ✗ {字段}: 缺失")
            return False
    
    # 检查元数据字段
    元数据必需字段 = ['作者', '来源', '摘要', '标签', '分类']
    for 字段 in 元数据必需字段:
        if 字段 in 报告数据['元数据']:
            print(f"   ✓ 元数据.{字段}: 存在")
        else:
            print(f"   ✗ 元数据.{字段}: 缺失")
    
    # 显示报告信息
    print(f"\n📋 报告信息:")
    print(f"   标题: {报告数据['标题']}")
    print(f"   内容长度: {len(报告数据['内容'])} 字符")
    print(f"   摘要: {报告数据['元数据']['摘要']}")
    print(f"   作者: {报告数据['元数据']['作者']}")
    print(f"   标签: {', '.join(报告数据['元数据']['标签'])}")
    
    # 保存测试报告
    当前目录 = os.path.dirname(__file__)
    根目录 = os.path.dirname(os.path.dirname(当前目录))
    测试报告路径 = os.path.join(根目录, "采集结果", f"测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    # 确保目录存在
    os.makedirs(os.path.dirname(测试报告路径), exist_ok=True)
    
    with open(测试报告路径, 'w', encoding='utf-8') as f:
        json.dump(报告数据, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试报告已保存: {测试报告路径}")
    
    # 显示内容预览
    print(f"\n📖 内容预览:")
    print("-" * 40)
    预览内容 = 报告数据['内容'][:300]
    if len(报告数据['内容']) > 300:
        预览内容 += "..."
    print(预览内容)
    
    print("\n🎉 测试完成！报告格式符合微信发布器要求")
    return True


def 测试微信发布器兼容性():
    """测试与微信发布器的兼容性"""
    print("\n🔗 测试与微信发布器的兼容性")
    print("-" * 40)
    
    try:
        # 尝试导入微信发布器相关模块
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '自动发布器'))
        
        # 创建提取器和测试数据
        提取器 = 套利信息提取器()
        测试数据 = 创建测试数据()
        
        # 生成文章数据
        文章数据 = 提取器.生成微信发布文章(测试数据)
        
        # 验证文章数据结构是否符合微信发布器要求
        print("✅ 文章数据结构验证:")
        print(f"   - 标题类型: {type(文章数据.get('标题'))}")
        print(f"   - 内容类型: {type(文章数据.get('内容'))}")
        print(f"   - 元数据类型: {type(文章数据.get('元数据'))}")
        
        # 检查内容是否为空
        if not 文章数据.get('内容'):
            print("❌ 错误: 内容为空！")
            return False
        
        if len(文章数据.get('内容', '')) < 100:
            print("❌ 错误: 内容过短！")
            return False
        
        print("✅ 兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"⚠️  兼容性测试异常: {str(e)}")
        return False


if __name__ == "__main__":
    # 运行测试
    格式测试结果 = 测试报告格式()
    兼容性测试结果 = 测试微信发布器兼容性()
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   报告格式测试: {'✅ 通过' if 格式测试结果 else '❌ 失败'}")
    print(f"   兼容性测试: {'✅ 通过' if 兼容性测试结果 else '❌ 失败'}")
    
    if 格式测试结果 and 兼容性测试结果:
        print("\n🎉 所有测试通过！套利信息提取器已成功适配微信发布器格式")
    else:
        print("\n❌ 部分测试失败，需要进一步调整")
