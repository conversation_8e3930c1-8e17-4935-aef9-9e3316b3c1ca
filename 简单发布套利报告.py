# -*- coding: utf-8 -*-
"""
简单的套利报告发布脚本

作者: AI助手
日期: 2025-07-29
功能: 生成套利报告并提供发布代码
"""

import sys
import os
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '内容收集器'))

def 生成并保存套利报告():
    """生成套利报告并保存为发布就绪格式"""
    print("🔍 正在生成套利报告...")
    
    try:
        from 套利信息提取器 import 套利信息提取器
        
        # 创建提取器
        提取器 = 套利信息提取器()
        
        # 设置文章目录
        当前目录 = os.path.dirname(os.path.abspath(__file__))
        文章目录 = os.path.join(当前目录, "采集结果", "微信文章")
        
        print(f"📁 分析目录: {文章目录}")
        
        # 批量提取套利信息
        套利信息列表 = 提取器.批量提取(文章目录)
        
        if not 套利信息列表:
            print("❌ 未找到可分析的文章，创建示例报告...")
            # 创建示例数据
            套利信息列表 = [{
                '文件名': '示例文章.txt',
                '标题': '今日套利机会分析',
                '发布时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                '新股信息': [{'股票名称': '科技创新', '相关信息': '科技创新今日申购，预计收益15%'}],
                '套利机会': ['新股申购机会较好'],
                '操作建议': ['建议积极参与申购']
            }]
        
        # 生成微信发布格式的报告
        报告数据 = 提取器.生成微信发布文章(套利信息列表)
        
        # 保存报告文件
        当前时间 = datetime.now().strftime('%Y%m%d_%H%M%S')
        报告文件名 = f"套利报告_{当前时间}.json"
        
        with open(报告文件名, 'w', encoding='utf-8') as f:
            json.dump(报告数据, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 报告生成成功！")
        print(f"📄 共分析 {len(套利信息列表)} 篇文章")
        print(f"📋 标题: {报告数据['标题']}")
        print(f"📝 内容长度: {len(报告数据['内容'])} 字符")
        print(f"💾 报告文件: {报告文件名}")
        
        return 报告文件名, 报告数据
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

def 显示发布代码(报告文件名):
    """显示如何使用微信自动发布器发布的代码"""
    print("\n" + "="*60)
    print("📖 如何使用您的微信自动发布器发布")
    print("="*60)
    
    print("\n🔧 方法1: 在Python中直接运行")
    print("-" * 40)
    print("```python")
    print("import json")
    print("import sys")
    print("import os")
    print("")
    print("# 添加路径")
    print("sys.path.append('源代码/自动发布器')")
    print("sys.path.append('配置文件')")
    print("")
    print("# 导入微信发布器")
    print("from 微信自动发布器 import 微信自动发布器")
    print("")
    print("# 加载套利报告")
    print(f"with open('{报告文件名}', 'r', encoding='utf-8') as f:")
    print("    报告数据 = json.load(f)")
    print("")
    print("# 创建发布器")
    print("发布器 = 微信自动发布器()")
    print("")
    print("# 发布文章（先创建草稿）")
    print("发布选项 = {")
    print("    '使用模板': True,      # 重要：内容已格式化")
    print("    '仅草稿': True,        # 先创建草稿")
    print("    '启用AI配图': True,    # 启用配图")
    print("    '显示封面': True")
    print("}")
    print("")
    print("结果 = 发布器.发布文章(报告数据, 发布选项)")
    print("print(f'发布结果: {结果}')")
    print("```")
    
    print("\n🔧 方法2: 创建发布脚本")
    print("-" * 40)
    
    发布脚本内容 = f'''# -*- coding: utf-8 -*-
import json
import sys
import os

# 添加路径
sys.path.append('源代码/自动发布器')
sys.path.append('配置文件')

try:
    from 微信自动发布器 import 微信自动发布器
    
    # 加载套利报告
    with open('{报告文件名}', 'r', encoding='utf-8') as f:
        报告数据 = json.load(f)
    
    print(f"📋 准备发布: {{报告数据['标题']}}")
    
    # 创建发布器
    发布器 = 微信自动发布器()
    
    # 发布选项
    发布选项 = {{
        '使用模板': True,      # 重要：内容已格式化
        '仅草稿': True,        # 先创建草稿
        '启用AI配图': True,    # 启用配图
        '显示封面': True
    }}
    
    # 发布文章
    结果 = 发布器.发布文章(报告数据, 发布选项)
    
    if 结果['success']:
        print("🎉 发布成功！")
        print(f"📄 草稿ID: {{结果.get('media_id', 'N/A')}}")
        print("💡 请到微信公众号后台查看草稿")
    else:
        print(f"❌ 发布失败: {{结果.get('error_message', '未知错误')}}")
        
except Exception as e:
    print(f"❌ 发布异常: {{str(e)}}")
    import traceback
    traceback.print_exc()
'''
    
    脚本文件名 = f"发布_{报告文件名.replace('.json', '.py')}"
    with open(脚本文件名, 'w', encoding='utf-8') as f:
        f.write(发布脚本内容)
    
    print(f"✅ 发布脚本已创建: {脚本文件名}")
    print(f"💡 运行命令: python {脚本文件名}")

def 显示报告预览(报告数据):
    """显示报告内容预览"""
    print("\n" + "="*60)
    print("📄 报告内容预览")
    print("="*60)
    
    print(f"标题: {报告数据['标题']}")
    print(f"作者: {报告数据['元数据']['作者']}")
    print(f"摘要: {报告数据['元数据']['摘要']}")
    print(f"标签: {', '.join(报告数据['元数据']['标签'])}")
    
    print("\n内容预览:")
    print("-" * 40)
    内容预览 = 报告数据['内容'][:500]
    if len(报告数据['内容']) > 500:
        内容预览 += "\n..."
    print(内容预览)

def 主函数():
    """主函数"""
    print("🚀 套利报告生成器")
    print("=" * 50)
    print("为您的微信自动发布器生成套利分析报告")
    print("")
    
    # 生成报告
    报告文件名, 报告数据 = 生成并保存套利报告()
    
    if not 报告文件名:
        print("❌ 报告生成失败")
        return
    
    # 显示报告预览
    显示报告预览(报告数据)
    
    # 显示发布代码
    显示发布代码(报告文件名)
    
    print("\n" + "="*60)
    print("🎯 总结")
    print("="*60)
    print("✅ 套利报告已生成并保存")
    print("✅ 报告格式完全兼容您的微信自动发布器")
    print("✅ 可以直接使用上述代码进行发布")
    print("✅ 建议先创建草稿，确认无误后再发布")
    
    print(f"\n📁 生成的文件:")
    print(f"   - 报告文件: {报告文件名}")
    print(f"   - 发布脚本: 发布_{报告文件名.replace('.json', '.py')}")

if __name__ == "__main__":
    主函数()
