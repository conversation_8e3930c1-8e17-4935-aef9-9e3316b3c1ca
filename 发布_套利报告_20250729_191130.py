# -*- coding: utf-8 -*-
import json
import sys
import os

# 添加路径
sys.path.append('源代码/自动发布器')
sys.path.append('配置文件')

try:
    from 微信自动发布器 import 微信自动发布器
    
    # 加载套利报告
    with open('套利报告_20250729_191130.json', 'r', encoding='utf-8') as f:
        报告数据 = json.load(f)
    
    print(f"📋 准备发布: {报告数据['标题']}")
    
    # 创建发布器
    发布器 = 微信自动发布器()
    
    # 发布选项
    发布选项 = {
        '使用模板': True,      # 重要：内容已格式化
        '仅草稿': True,        # 先创建草稿
        '启用AI配图': True,    # 启用配图
        '显示封面': True
    }
    
    # 发布文章
    结果 = 发布器.发布文章(报告数据, 发布选项)
    
    if 结果['success']:
        print("🎉 发布成功！")
        print(f"📄 草稿ID: {结果.get('media_id', 'N/A')}")
        print("💡 请到微信公众号后台查看草稿")
    else:
        print(f"❌ 发布失败: {结果.get('error_message', '未知错误')}")
        
except Exception as e:
    print(f"❌ 发布异常: {str(e)}")
    import traceback
    traceback.print_exc()
