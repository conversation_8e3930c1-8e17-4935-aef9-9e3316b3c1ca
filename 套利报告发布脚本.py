# -*- coding: utf-8 -*-
"""
套利报告发布脚本

作者: AI助手
日期: 2025-07-29
功能: 完整的套利信息提取和微信发布流程
"""

import sys
import os
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '内容收集器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '配置文件'))

def 步骤1_生成套利报告():
    """步骤1: 生成套利报告"""
    print("🔍 步骤1: 生成套利报告")
    print("-" * 40)
    
    try:
        from 套利信息提取器 import 套利信息提取器
        
        # 创建提取器
        提取器 = 套利信息提取器()
        
        # 设置文章目录
        当前目录 = os.path.dirname(os.path.abspath(__file__))
        文章目录 = os.path.join(当前目录, "采集结果", "微信文章")
        
        print(f"📁 分析目录: {文章目录}")
        
        # 检查目录是否存在
        if not os.path.exists(文章目录):
            print("❌ 文章目录不存在，创建示例数据...")
            return 创建示例报告()
        
        # 批量提取套利信息
        套利信息列表 = 提取器.批量提取(文章目录)
        
        if not 套利信息列表:
            print("❌ 未找到可分析的文章，创建示例数据...")
            return 创建示例报告()
        
        # 生成报告
        报告数据 = 提取器.生成微信发布文章(套利信息列表)
        
        # 保存报告
        报告文件路径 = 提取器.保存发布就绪文章(套利信息列表)
        
        print(f"✅ 报告生成成功！")
        print(f"📄 共分析 {len(套利信息列表)} 篇文章")
        print(f"📁 报告文件: {报告文件路径}")
        print(f"📋 标题: {报告数据['标题']}")
        print(f"📝 内容长度: {len(报告数据['内容'])} 字符")
        
        return 报告文件路径, 报告数据
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        print("🔄 创建示例报告...")
        return 创建示例报告()

def 创建示例报告():
    """创建示例报告用于演示"""
    from 套利信息提取器 import 套利信息提取器
    
    # 创建示例数据
    示例数据 = [
        {
            '文件名': '示例套利文章.txt',
            '标题': '今日套利机会分析',
            '发布时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            '链接': 'https://example.com/article',
            '新股信息': [
                {
                    '股票名称': '科技创新',
                    '相关信息': '科技创新今日申购，发行价格25.8元，预计收益率15%，建议积极参与申购'
                }
            ],
            'REIT信息': [
                {
                    'REIT名称': '产业园REIT',
                    '相关信息': '产业园REIT本周上市，年化收益率预期4.5%，适合稳健投资者配置'
                }
            ],
            '转债信息': [
                {
                    '转债名称': '科技转债',
                    '相关信息': '科技转债当前价格98元，转股溢价率5%，具有一定套利空间'
                }
            ],
            '套利机会': [
                '新股申购中签率预计0.5%，预期收益15%，年化收益率可观',
                'REIT折价发行，上市后预期有5-10%涨幅空间'
            ],
            '操作建议': [
                '建议满额申购新股，提高中签概率',
                '关注REIT上市首日表现，择机介入'
            ],
            '时间节点': [
                '今日16:00前完成新股申购',
                '明日公布中签结果'
            ]
        }
    ]
    
    提取器 = 套利信息提取器()
    报告数据 = 提取器.生成微信发布文章(示例数据)
    报告文件路径 = 提取器.保存发布就绪文章(示例数据)
    
    print(f"✅ 示例报告创建成功！")
    print(f"📁 报告文件: {报告文件路径}")
    
    return 报告文件路径, 报告数据

def 步骤2_验证配置():
    """步骤2: 验证微信发布器配置"""
    print("\n🔧 步骤2: 验证微信发布器配置")
    print("-" * 40)
    
    try:
        from 微信发布配置 import 验证配置, 显示配置摘要
        
        # 显示配置摘要
        显示配置摘要()
        
        # 验证配置
        配置有效, 错误列表 = 验证配置()
        
        if 配置有效:
            print("✅ 配置验证通过")
            return True
        else:
            print("❌ 配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            return False
            
    except Exception as e:
        print(f"❌ 配置验证异常: {str(e)}")
        return False

def 步骤3_发布文章(报告文件路径, 报告数据):
    """步骤3: 发布文章到微信公众号"""
    print("\n🚀 步骤3: 发布文章到微信公众号")
    print("-" * 40)
    
    try:
        from 微信自动发布器 import 微信自动发布器
        
        # 创建发布器
        发布器 = 微信自动发布器()
        
        # 验证系统配置
        配置有效, 错误列表 = 发布器.验证系统配置()
        if not 配置有效:
            print("❌ 系统配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            return False
        
        print("✅ 系统配置验证通过")
        
        # 发布选项
        发布选项 = {
            '使用模板': True,      # 重要：告诉发布器内容已经格式化
            '仅草稿': True,        # 先创建草稿，避免直接发布
            '启用AI配图': False,    # 可选：是否启用AI配图
            '显示封面': True,      # 显示封面图
            '开启评论': False,     # 关闭评论
            '仅粉丝评论': False    # 不限制粉丝评论
        }
        
        print(f"📝 准备发布文章: {报告数据['标题']}")
        print(f"📄 内容长度: {len(报告数据['内容'])} 字符")
        print(f"📋 发布选项: {发布选项}")
        
        # 发布文章
        print("\n🔄 开始发布...")
        结果 = 发布器.发布文章(报告数据, 发布选项)
        
        # 处理结果
        if 结果['success']:
            print("🎉 发布成功！")
            print(f"📄 草稿ID: {结果.get('media_id', 'N/A')}")
            if 结果.get('draft_only'):
                print("📝 文章已保存为草稿，可在微信公众号后台查看")
            else:
                print(f"🚀 文章已发布，发布ID: {结果.get('publish_id', 'N/A')}")
            
            print(f"⏱️  处理耗时: {结果.get('process_time', 0):.2f} 秒")
            
            return True
        else:
            print(f"❌ 发布失败: {结果.get('error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 发布异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 显示使用说明():
    """显示使用说明"""
    print("\n" + "=" * 60)
    print("📚 使用说明")
    print("=" * 60)
    
    print("\n🔄 自动化流程:")
    print("1. 运行此脚本会自动执行完整的发布流程")
    print("2. 首先分析采集的文章，生成套利报告")
    print("3. 然后验证微信发布器配置")
    print("4. 最后将报告发布到微信公众号（默认为草稿）")
    
    print("\n⚙️ 配置要求:")
    print("- 确保微信API配置正确（AppID、AppSecret等）")
    print("- 检查网络连接，能够访问微信API")
    print("- 确保有足够的API调用次数")
    
    print("\n📝 发布选项:")
    print("- 默认创建草稿，不直接发布")
    print("- 可在微信公众号后台预览和手动发布")
    print("- 如需直接发布，修改 '仅草稿': False")
    
    print("\n🔍 故障排除:")
    print("- 如果配置验证失败，检查配置文件")
    print("- 如果发布失败，查看错误信息")
    print("- 可以先运行测试模式验证功能")

def 主函数():
    """主函数"""
    print("🚀 套利报告自动发布系统")
    print("=" * 60)
    
    # 步骤1: 生成套利报告
    报告结果 = 步骤1_生成套利报告()
    if not 报告结果:
        print("❌ 报告生成失败，无法继续")
        return
    
    报告文件路径, 报告数据 = 报告结果
    
    # 步骤2: 验证配置
    if not 步骤2_验证配置():
        print("❌ 配置验证失败，建议先修复配置问题")
        print("💡 提示: 可以查看生成的报告文件进行手动发布")
        print(f"📁 报告文件: {报告文件路径}")
        return
    
    # 步骤3: 发布文章
    发布成功 = 步骤3_发布文章(报告文件路径, 报告数据)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("📊 执行结果")
    print("=" * 60)
    
    if 发布成功:
        print("🎉 套利报告发布流程执行成功！")
        print("📝 文章已创建为草稿，请到微信公众号后台查看")
    else:
        print("❌ 发布流程未完全成功")
        print("💡 建议检查配置和网络连接")
    
    print(f"📁 生成的报告文件: {报告文件路径}")
    
    # 显示使用说明
    显示使用说明()

if __name__ == "__main__":
    主函数()
