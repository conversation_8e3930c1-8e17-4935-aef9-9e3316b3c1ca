# -*- coding: utf-8 -*-
"""
套利信息提取器

作者: AI助手
日期: 2025-07-29
功能: 从采集的文章中提取套利相关信息
"""

import os
import re
from datetime import datetime
from typing import List, Dict, Any
from bs4 import BeautifulSoup


class 套利信息提取器:
    """从文章中提取套利信息"""
    
    def __init__(self):
        self.套利关键词 = [
            '申购', '打新', '新股', '转债', 'REIT', '套利', '收益',
            '涨幅', '涨停', '上市', '中签', '配售', '折价', '溢价',
            '正股', '碎股', '门槛', '资金', '概率', '预期'
        ]
        
        self.数字模式 = [
            r'(\d+\.?\d*)%',  # 百分比
            r'(\d+\.?\d*)万',  # 万
            r'(\d+\.?\d*)亿',  # 亿
            r'(\d+\.?\d*)元',  # 元
            r'(\d+\.?\d*)w',   # w表示万
        ]
    
    def 提取文章套利信息(self, 文件路径: str) -> Dict[str, Any]:
        """提取单篇文章的套利信息"""
        try:
            with open(文件路径, 'r', encoding='utf-8') as f:
                内容 = f.read()
            
            # 分离元数据和正文
            lines = 内容.split('\n')
            元数据 = {}
            正文开始 = 0
            
            for i, line in enumerate(lines):
                if line.startswith('='):
                    正文开始 = i + 1
                    break
                elif ':' in line:
                    key, value = line.split(':', 1)
                    元数据[key.strip()] = value.strip()
            
            正文 = '\n'.join(lines[正文开始:])
            
            # 清理HTML标签
            正文 = self._清理HTML(正文)
            
            # 提取套利信息
            套利信息 = {
                '文件名': os.path.basename(文件路径),
                '标题': 元数据.get('标题', ''),
                '发布时间': 元数据.get('发布时间', ''),
                '链接': 元数据.get('链接', ''),
                '新股信息': self._提取新股信息(正文),
                'REIT信息': self._提取REIT信息(正文),
                '转债信息': self._提取转债信息(正文),
                '套利机会': self._提取套利机会(正文),
                '重要数据': self._提取重要数据(正文),
                '操作建议': self._提取操作建议(正文),
                '时间节点': self._提取时间节点(正文)
            }
            
            return 套利信息
            
        except Exception as e:
            print(f"❌ 提取文件 {文件路径} 失败: {e}")
            return {}
    
    def _清理HTML(self, 文本: str) -> str:
        """清理HTML标签，保留纯文本"""
        try:
            soup = BeautifulSoup(文本, 'html.parser')
            return soup.get_text()
        except:
            # 如果BeautifulSoup失败，使用正则表达式
            文本 = re.sub(r'<[^>]+>', '', 文本)
            文本 = re.sub(r'&[a-zA-Z]+;', '', 文本)
            return 文本
    
    def _提取新股信息(self, 文本: str) -> List[Dict[str, str]]:
        """提取新股相关信息"""
        新股信息 = []
        
        # 查找新股名称
        新股模式 = [
            r'([^，。！？\s]{2,6})(申购|打新|上市)',
            r'(酉立智能|鼎佳精密|长江能科)',
        ]
        
        for 模式 in 新股模式:
            matches = re.findall(模式, 文本)
            for match in matches:
                if isinstance(match, tuple):
                    股票名称 = match[0]
                else:
                    股票名称 = match
                
                # 查找相关信息
                相关信息 = self._查找相关信息(文本, 股票名称)
                
                新股信息.append({
                    '股票名称': 股票名称,
                    '相关信息': 相关信息
                })
        
        return 新股信息
    
    def _提取REIT信息(self, 文本: str) -> List[Dict[str, str]]:
        """提取REIT相关信息"""
        REIT信息 = []
        
        # 查找REIT名称
        REIT模式 = r'([^，。！？\s]{2,8}REIT)'
        matches = re.findall(REIT模式, 文本)
        
        for REIT名称 in matches:
            相关信息 = self._查找相关信息(文本, REIT名称)
            REIT信息.append({
                'REIT名称': REIT名称,
                '相关信息': 相关信息
            })
        
        return REIT信息
    
    def _提取转债信息(self, 文本: str) -> List[Dict[str, str]]:
        """提取转债相关信息"""
        转债信息 = []
        
        # 查找转债名称
        转债模式 = r'([^，。！？\s]{2,6}转债)'
        matches = re.findall(转债模式, 文本)
        
        for 转债名称 in matches:
            相关信息 = self._查找相关信息(文本, 转债名称)
            转债信息.append({
                '转债名称': 转债名称,
                '相关信息': 相关信息
            })
        
        return 转债信息
    
    def _提取套利机会(self, 文本: str) -> List[str]:
        """提取套利机会描述"""
        套利机会 = []
        
        套利模式 = [
            r'([^。！？]*套利[^。！？]*)',
            r'([^。！？]*折价[^。！？]*)',
            r'([^。！？]*收益[^。！？]*)',
            r'([^。！？]*概率[^。！？]*)',
        ]
        
        for 模式 in 套利模式:
            matches = re.findall(模式, 文本)
            套利机会.extend(matches)
        
        return list(set(套利机会))  # 去重
    
    def _提取重要数据(self, 文本: str) -> List[str]:
        """提取重要数据"""
        重要数据 = []
        
        for 模式 in self.数字模式:
            matches = re.findall(模式, 文本)
            for match in matches:
                # 查找包含这个数字的句子
                句子模式 = rf'[^。！？]*{re.escape(match)}[^。！？]*'
                句子 = re.findall(句子模式, 文本)
                重要数据.extend(句子)
        
        return list(set(重要数据))  # 去重
    
    def _提取操作建议(self, 文本: str) -> List[str]:
        """提取操作建议"""
        操作建议 = []
        
        建议模式 = [
            r'([^。！？]*建议[^。！？]*)',
            r'([^。！？]*推荐[^。！？]*)',
            r'([^。！？]*可以[^。！？]*)',
            r'([^。！？]*应该[^。！？]*)',
            r'([^。！？]*计划[^。！？]*)',
            r'([^。！？]*策略[^。！？]*)',
        ]
        
        for 模式 in 建议模式:
            matches = re.findall(模式, 文本)
            操作建议.extend(matches)
        
        return list(set(操作建议))  # 去重
    
    def _提取时间节点(self, 文本: str) -> List[str]:
        """提取重要时间节点"""
        时间节点 = []
        
        时间模式 = [
            r'(明天[^。！？]*)',
            r'(今天[^。！？]*)',
            r'(周[一二三四五六日][^。！？]*)',
            r'(\d+月\d+[日号][^。！？]*)',
            r'(\d+年\d+月\d+[日号][^。！？]*)',
        ]
        
        for 模式 in 时间模式:
            matches = re.findall(模式, 文本)
            时间节点.extend(matches)
        
        return list(set(时间节点))  # 去重
    
    def _查找相关信息(self, 文本: str, 关键词: str) -> str:
        """查找关键词相关的信息"""
        # 查找包含关键词的句子
        句子模式 = rf'[^。！？]*{re.escape(关键词)}[^。！？]*'
        句子列表 = re.findall(句子模式, 文本)
        
        return '；'.join(句子列表[:3])  # 最多返回3个相关句子
    
    def 批量提取(self, 目录路径: str) -> List[Dict[str, Any]]:
        """批量提取目录下所有文章的套利信息"""
        所有套利信息 = []
        
        if not os.path.exists(目录路径):
            print(f"❌ 目录不存在: {目录路径}")
            return []
        
        for 文件名 in os.listdir(目录路径):
            if 文件名.endswith('.txt'):
                文件路径 = os.path.join(目录路径, 文件名)
                套利信息 = self.提取文章套利信息(文件路径)
                if 套利信息:
                    所有套利信息.append(套利信息)
        
        return 所有套利信息
    
    def 生成套利报告(self, 套利信息列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成套利信息报告，返回符合微信发布器格式的数据结构"""

        # 生成报告标题
        当前时间 = datetime.now()
        报告标题 = f"📊 套利信息分析报告 - {当前时间.strftime('%m月%d日')}"

        # 构建报告内容
        报告内容 = []

        # 报告头部
        报告内容.append("# 📊 套利信息分析报告")
        报告内容.append("")
        报告内容.append(f"**📅 生成时间**: {当前时间.strftime('%Y年%m月%d日 %H:%M')}")
        报告内容.append(f"**📄 分析文章数**: {len(套利信息列表)} 篇")
        报告内容.append("")
        报告内容.append("---")
        报告内容.append("")

        # 汇总信息
        所有新股 = []
        所有REIT = []
        所有转债 = []
        所有套利机会 = []
        所有操作建议 = []
        所有时间节点 = []

        # 收集所有信息
        for 信息 in 套利信息列表:
            if 信息.get('新股信息'):
                所有新股.extend(信息['新股信息'])
            if 信息.get('REIT信息'):
                所有REIT.extend(信息['REIT信息'])
            if 信息.get('转债信息'):
                所有转债.extend(信息['转债信息'])
            if 信息.get('套利机会'):
                所有套利机会.extend(信息['套利机会'])
            if 信息.get('操作建议'):
                所有操作建议.extend(信息['操作建议'])
            if 信息.get('时间节点'):
                所有时间节点.extend(信息['时间节点'])

        # 去重并限制数量
        所有新股 = list({新股['股票名称']: 新股 for 新股 in 所有新股}.values())[:10]
        所有REIT = list({REIT['REIT名称']: REIT for REIT in 所有REIT}.values())[:10]
        所有转债 = list({转债['转债名称']: 转债 for 转债 in 所有转债}.values())[:10]
        所有套利机会 = list(set(所有套利机会))[:8]
        所有操作建议 = list(set(所有操作建议))[:8]
        所有时间节点 = list(set(所有时间节点))[:8]

        # 生成汇总内容
        if 所有新股:
            报告内容.append("## 🆕 新股申购机会")
            报告内容.append("")
            for 新股 in 所有新股:
                报告内容.append(f"**{新股['股票名称']}**")
                if 新股.get('相关信息'):
                    报告内容.append(f"- {新股['相关信息'][:150]}")
                报告内容.append("")

        if 所有REIT:
            报告内容.append("## 🏢 REIT投资机会")
            报告内容.append("")
            for REIT in 所有REIT:
                报告内容.append(f"**{REIT['REIT名称']}**")
                if REIT.get('相关信息'):
                    报告内容.append(f"- {REIT['相关信息'][:150]}")
                报告内容.append("")

        if 所有转债:
            报告内容.append("## 💰 转债套利机会")
            报告内容.append("")
            for 转债 in 所有转债:
                报告内容.append(f"**{转债['转债名称']}**")
                if 转债.get('相关信息'):
                    报告内容.append(f"- {转债['相关信息'][:150]}")
                报告内容.append("")

        if 所有套利机会:
            报告内容.append("## 💡 套利机会分析")
            报告内容.append("")
            for 机会 in 所有套利机会:
                报告内容.append(f"- {机会[:200]}")
            报告内容.append("")

        if 所有操作建议:
            报告内容.append("## 📋 操作建议")
            报告内容.append("")
            for 建议 in 所有操作建议:
                报告内容.append(f"- {建议[:200]}")
            报告内容.append("")

        if 所有时间节点:
            报告内容.append("## ⏰ 重要时间节点")
            报告内容.append("")
            for 时间 in 所有时间节点:
                报告内容.append(f"- {时间[:200]}")
            报告内容.append("")

        # 添加免责声明
        报告内容.append("---")
        报告内容.append("")
        报告内容.append("## ⚠️ 风险提示")
        报告内容.append("")
        报告内容.append("- 本报告仅供参考，不构成投资建议")
        报告内容.append("- 投资有风险，入市需谨慎")
        报告内容.append("- 请根据自身风险承受能力做出投资决策")
        报告内容.append("")
        报告内容.append(f"*报告生成时间：{当前时间.strftime('%Y-%m-%d %H:%M:%S')}*")

        # 生成摘要
        摘要内容 = []
        if 所有新股:
            摘要内容.append(f"新股{len(所有新股)}只")
        if 所有REIT:
            摘要内容.append(f"REIT{len(所有REIT)}只")
        if 所有转债:
            摘要内容.append(f"转债{len(所有转债)}只")
        if 所有套利机会:
            摘要内容.append(f"套利机会{len(所有套利机会)}个")

        摘要 = f"本期分析{len(套利信息列表)}篇文章，发现" + "、".join(摘要内容) + "等投资机会。"

        # 返回符合微信发布器格式的数据结构
        return {
            '标题': 报告标题,
            '内容': '\n'.join(报告内容),
            '元数据': {
                '作者': 'AI套利分析师',
                '来源': '套利信息提取器',
                '摘要': 摘要,
                '标签': ['套利', '投资', '新股', 'REIT', '转债'],
                '分类': '投资分析',
                '生成时间': 当前时间.isoformat(),
                '分析文章数': len(套利信息列表)
            }
        }

    def 生成微信发布文章(self, 套利信息列表: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成可直接用于微信发布的文章数据"""
        return self.生成套利报告(套利信息列表)

    def 保存发布就绪文章(self, 套利信息列表: List[Dict[str, Any]], 保存路径: str = None) -> str:
        """保存可直接发布的文章数据"""
        import json

        if not 保存路径:
            当前目录 = os.path.dirname(os.path.abspath(__file__))
            根目录 = os.path.dirname(os.path.dirname(当前目录))
            保存路径 = os.path.join(根目录, "采集结果", f"发布就绪_套利报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

        文章数据 = self.生成微信发布文章(套利信息列表)

        with open(保存路径, 'w', encoding='utf-8') as f:
            json.dump(文章数据, f, ensure_ascii=False, indent=2)

        return 保存路径


def 主函数():
    """主函数"""
    print("🔍 套利信息提取器")
    print("=" * 50)

    # 创建提取器
    提取器 = 套利信息提取器()

    # 设置文章目录
    当前目录 = os.path.dirname(os.path.abspath(__file__))
    根目录 = os.path.dirname(os.path.dirname(当前目录))
    文章目录 = os.path.join(根目录, "采集结果", "微信文章")

    print(f"📁 分析目录: {文章目录}")

    # 批量提取套利信息
    套利信息列表 = 提取器.批量提取(文章目录)

    if not 套利信息列表:
        print("❌ 未找到可分析的文章")
        return

    # 生成报告（新格式，符合微信发布器要求）
    报告数据 = 提取器.生成套利报告(套利信息列表)

    # 保存为JSON格式，供微信发布器使用
    import json
    报告JSON文件 = os.path.join(根目录, "采集结果", f"套利分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")

    with open(报告JSON文件, 'w', encoding='utf-8') as f:
        json.dump(报告数据, f, ensure_ascii=False, indent=2)

    # 同时保存为文本格式，便于查看
    报告文本文件 = os.path.join(根目录, "采集结果", f"套利分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")

    with open(报告文本文件, 'w', encoding='utf-8') as f:
        f.write(f"标题: {报告数据['标题']}\n")
        f.write("=" * 50 + "\n\n")
        f.write(报告数据['内容'])
        f.write("\n\n" + "=" * 50 + "\n")
        f.write("元数据:\n")
        for key, value in 报告数据['元数据'].items():
            f.write(f"  {key}: {value}\n")

    print(f"✅ 分析完成！")
    print(f"📄 共分析 {len(套利信息列表)} 篇文章")
    print(f"📁 JSON报告已保存: {报告JSON文件}")
    print(f"📁 文本报告已保存: {报告文本文件}")

    # 显示报告摘要
    print("\n" + "="*60)
    print("📊 套利信息分析结果:")
    print("="*60)
    print(f"标题: {报告数据['标题']}")
    print(f"摘要: {报告数据['元数据']['摘要']}")
    print(f"内容长度: {len(报告数据['内容'])} 字符")
    print("\n预览内容:")
    print("-" * 40)
    # 显示内容的前500个字符作为预览
    预览内容 = 报告数据['内容'][:500]
    if len(报告数据['内容']) > 500:
        预览内容 += "..."
    print(预览内容)


if __name__ == "__main__":
    主函数()
