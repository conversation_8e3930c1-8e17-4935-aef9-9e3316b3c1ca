# -*- coding: utf-8 -*-
"""
使用现有的微信自动发布器发布套利报告

作者: AI助手
日期: 2025-07-29
功能: 将套利信息提取器生成的报告通过微信自动发布器发布
"""

import sys
import os
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '内容收集器'))
sys.path.append(os.path.join(os.path.dirname(__file__), '源代码', '自动发布器'))

def 生成套利报告():
    """生成套利报告"""
    print("🔍 正在生成套利报告...")
    
    try:
        from 套利信息提取器 import 套利信息提取器
        
        # 创建提取器
        提取器 = 套利信息提取器()
        
        # 设置文章目录
        当前目录 = os.path.dirname(os.path.abspath(__file__))
        文章目录 = os.path.join(当前目录, "采集结果", "微信文章")
        
        print(f"📁 分析目录: {文章目录}")
        
        # 批量提取套利信息
        套利信息列表 = 提取器.批量提取(文章目录)
        
        if not 套利信息列表:
            print("❌ 未找到可分析的文章")
            return None
        
        # 生成微信发布格式的报告
        报告数据 = 提取器.生成微信发布文章(套利信息列表)
        
        print(f"✅ 报告生成成功！")
        print(f"📄 共分析 {len(套利信息列表)} 篇文章")
        print(f"📋 标题: {报告数据['标题']}")
        print(f"📝 内容长度: {len(报告数据['内容'])} 字符")
        
        return 报告数据
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")
        return None

def 使用微信发布器发布(报告数据):
    """使用现有的微信自动发布器发布报告"""
    print("\n🚀 正在使用微信自动发布器发布...")
    
    try:
        # 导入您现有的微信自动发布器
        from 微信自动发布器 import 微信自动发布器
        
        # 创建发布器实例
        发布器 = 微信自动发布器()
        
        # 验证系统配置
        print("🔧 验证系统配置...")
        配置有效, 错误列表 = 发布器.验证系统配置()
        
        if not 配置有效:
            print("❌ 系统配置验证失败:")
            for error in 错误列表:
                print(f"   - {error}")
            print("\n💡 建议:")
            print("   1. 检查配置文件中的微信API配置")
            print("   2. 确保网络连接正常")
            print("   3. 可以先运行测试模式")
            return False
        
        print("✅ 系统配置验证通过")
        
        # 设置发布选项
        发布选项 = {
            '使用模板': True,        # 重要：告诉发布器内容已经格式化，跳过重复处理
            '仅草稿': True,          # 建议先创建草稿，确认无误后再手动发布
            '启用AI配图': True,      # 启用AI配图功能
            '显示封面': True,        # 显示封面图
            '开启评论': False,       # 关闭评论功能
            '仅粉丝评论': False,     # 不限制粉丝评论
            '排版样式': 'business'   # 使用商务排版样式
        }
        
        print(f"📝 准备发布文章: {报告数据['标题']}")
        print(f"📋 发布选项: {发布选项}")
        
        # 发布文章
        print("\n🔄 开始发布...")
        结果 = 发布器.发布文章(报告数据, 发布选项)
        
        # 处理发布结果
        if 结果['success']:
            print("🎉 发布成功！")
            print(f"📄 草稿ID: {结果.get('media_id', 'N/A')}")
            
            if 结果.get('draft_only'):
                print("📝 文章已保存为草稿")
                print("💡 请到微信公众号后台查看和发布:")
                print("   1. 登录微信公众号后台")
                print("   2. 进入素材管理 -> 草稿箱")
                print("   3. 找到刚创建的草稿进行预览")
                print("   4. 确认无误后点击发布")
            else:
                print(f"🚀 文章已直接发布，发布ID: {结果.get('publish_id', 'N/A')}")
            
            print(f"⏱️  处理耗时: {结果.get('process_time', 0):.2f} 秒")
            
            # 显示发布统计
            统计信息 = 发布器.获取发布统计()
            print(f"📊 发布统计: 成功 {统计信息['successful_uploads']} 篇")
            
            return True
        else:
            print(f"❌ 发布失败: {结果.get('error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 发布异常: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def 手动发布模式():
    """手动发布模式 - 生成报告文件供手动使用"""
    print("\n📁 手动发布模式")
    print("-" * 40)
    
    # 生成报告
    报告数据 = 生成套利报告()
    if not 报告数据:
        return
    
    # 保存为JSON文件
    当前时间 = datetime.now().strftime('%Y%m%d_%H%M%S')
    文件名 = f"套利报告_手动发布_{当前时间}.json"
    
    with open(文件名, 'w', encoding='utf-8') as f:
        json.dump(报告数据, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 报告已保存: {文件名}")
    print("\n📖 手动发布步骤:")
    print("1. 在Python中运行以下代码:")
    print("```python")
    print("import json")
    print("import sys, os")
    print("sys.path.append('源代码/自动发布器')")
    print("from 微信自动发布器 import 微信自动发布器")
    print("")
    print(f"# 加载报告数据")
    print(f"with open('{文件名}', 'r', encoding='utf-8') as f:")
    print("    报告数据 = json.load(f)")
    print("")
    print("# 创建发布器并发布")
    print("发布器 = 微信自动发布器()")
    print("结果 = 发布器.发布文章(报告数据, {'使用模板': True, '仅草稿': True})")
    print("```")

def 主函数():
    """主函数"""
    print("🚀 套利报告发布工具")
    print("=" * 50)
    print("使用您现有的微信自动发布器发布套利分析报告")
    print("")
    
    # 询问发布模式
    print("请选择发布模式:")
    print("1. 自动发布模式（直接使用微信发布器）")
    print("2. 手动发布模式（生成文件供手动使用）")
    
    try:
        选择 = input("\n请输入选择 (1/2): ").strip()
        
        if 选择 == "1":
            print("\n🤖 自动发布模式")
            print("=" * 50)
            
            # 生成报告
            报告数据 = 生成套利报告()
            if not 报告数据:
                print("❌ 报告生成失败，无法继续")
                return
            
            # 使用微信发布器发布
            发布成功 = 使用微信发布器发布(报告数据)
            
            if 发布成功:
                print("\n🎉 发布流程完成！")
            else:
                print("\n❌ 发布失败，建议使用手动模式")
                
        elif 选择 == "2":
            手动发布模式()
            
        else:
            print("❌ 无效选择，请重新运行程序")
            
    except KeyboardInterrupt:
        print("\n\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")

if __name__ == "__main__":
    主函数()
